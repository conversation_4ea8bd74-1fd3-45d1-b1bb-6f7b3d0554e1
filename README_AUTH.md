# 双Token认证系统使用说明

## 概述

本项目实现了基于axios和双token（accessToken + refreshToken）的认证系统，支持自动token刷新和401错误重试机制。

## 主要特性

- ✅ 双token认证（accessToken + refreshToken）
- ✅ 自动token刷新机制
- ✅ 401错误自动重试
- ✅ 请求队列管理（刷新token时暂存请求）
- ✅ Pinia状态管理集成
- ✅ 本地存储持久化
- ✅ 完整的错误处理

## 核心文件结构

```
src/
├── services/
│   └── authService.js           # 认证服务（处理登录、登出、token刷新）
├── utils/
│   ├── auth.js                  # 认证相关工具函数
│   └── request/
│       └── uni-request.js       # 基于axios的请求工具
├── store/modules/
│   └── uesr.js                  # 用户状态管理（集成token管理）
├── apis/
│   └── auth.js                  # 认证相关API
└── pages/login/
    └── login.vue                # 登录页面
```

## 使用方法

### 1. 登录

```javascript
import { useUserStore } from '@/store/modules/uesr'

const userStore = useUserStore()

// 登录
try {
  await userStore.login({
    username: 'your_username',
    password: 'your_password'
  })
  console.log('登录成功')
} catch (error) {
  console.error('登录失败:', error.message)
}
```

### 2. 发起API请求

```javascript
import request from '@/utils/request/uni-request'

// GET请求
const userData = await request.get('/api/user/profile')

// POST请求
const result = await request.post('/api/user/update', {
  name: 'New Name'
})

// 不需要token的请求
const publicData = await request.get('/api/public/data', {}, {
  isNeedToken: false
})
```

### 3. 检查认证状态

```javascript
import { useUserStore } from '@/store/modules/uesr'
import { needLogin, checkPageAuth, getTokenRemainingTimeText } from '@/utils/auth'

const userStore = useUserStore()

// 检查是否已登录
if (userStore.hasValidToken) {
  console.log('用户已登录')
}

// 检查是否需要登录
if (needLogin()) {
  console.log('需要登录')
}

// 检查页面访问权限（自动跳转登录页）
if (checkPageAuth('/current/page/path')) {
  console.log('有访问权限')
}

// 获取token剩余时间
const remainingTime = getTokenRemainingTimeText()
console.log(`Token剩余时间: ${remainingTime}`)
```

### 4. 登出

```javascript
import { useUserStore } from '@/store/modules/uesr'

const userStore = useUserStore()

await userStore.logout()
```

## API接口要求

### 登录接口

**请求：** `POST /auth/login`

```json
{
  "username": "用户名",
  "password": "密码"
}
```

**响应：**

```json
{
  "code": 200,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 7200,
    "userInfo": {
      "id": 1,
      "username": "user",
      "name": "用户名"
    }
  }
}
```

### 刷新Token接口

**请求：** `POST /auth/refresh`

```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**响应：**

```json
{
  "code": 200,
  "data": {
    "accessToken": "new_access_token",
    "refreshToken": "new_refresh_token",
    "expiresIn": 7200
  }
}
```

### 登出接口

**请求：** `POST /auth/logout`

**响应：**

```json
{
  "code": 200,
  "message": "登出成功"
}
```

## 自动处理机制

### 1. Token过期自动刷新

当API返回401错误时，系统会：
1. 暂停当前请求
2. 使用refreshToken获取新的accessToken
3. 更新本地存储和store中的token
4. 重新发起原始请求
5. 处理请求队列中的其他请求

### 2. 请求队列管理

在token刷新期间，所有新的请求会被加入队列，等待token刷新完成后统一处理。

### 3. 错误处理

- 网络错误：显示"请检查当前网络环境"提示
- 401错误：自动刷新token或跳转登录页
- 其他错误：显示具体错误信息

## 配置说明

### 环境变量

在`.env`文件中配置API地址：

```
VITE_API_ADDRESS=https://your-api-domain.com
```

### Token过期时间

默认token过期时间为7200秒（2小时），系统会在过期前5分钟开始尝试刷新。

## 测试页面

访问 `/pages/test/authTest` 可以测试认证功能，包括：
- 查看token状态
- 测试受保护接口
- 手动刷新token
- 登出功能

## 注意事项

1. 确保后端API返回的数据结构与文档一致
2. 登录页面路径为 `/pages/login/login`
3. 系统会自动处理token的存储和刷新
4. 建议在应用启动时调用 `initAuth()` 初始化认证系统
5. 已修复微信小程序编译时动态导入的问题，现在使用静态导入

## 微信小程序兼容性

本认证系统已经针对微信小程序进行了优化：

- ✅ 移除了所有动态 `import()` 调用，改为静态导入
- ✅ 通过AuthService解决了循环依赖问题
- ✅ 使用依赖注入模式管理组件间关系
- ✅ 兼容小程序的模块系统

## 架构设计

新的架构采用了服务层模式来解决循环依赖：

```
┌─────────────────┐    ┌─────────────────┐
│   User Store    │◄──►│  Auth Service   │
└─────────────────┘    └─────────────────┘
         ▲                       ▲
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│  Request Tool   │    │    Auth API     │
└─────────────────┘    └─────────────────┘
```

- **AuthService**: 中央认证服务，处理所有认证逻辑
- **User Store**: 状态管理，通过依赖注入与AuthService通信
- **Request Tool**: 网络请求工具，使用AuthService获取token
- **Auth API**: 底层API调用，被AuthService使用
