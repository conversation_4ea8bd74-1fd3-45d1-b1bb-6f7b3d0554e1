<template>
  <view class="content">
    <image class="logo" src="@/static/logo.png"></image>
    <view class="text-area">
      <text class="title" @click="gotoIndex">{{ route.query.name }}</text>
    </view>
  </view>
</template>

<script setup>
import { useRouter,useRoute } from "uniapp-router-next";
import { ref } from "vue";
import { useTestStore } from "@/store/modules/test";
const router = useRouter();
const route = useRoute()
const title = ref("跳转首页");
// 接收传参
const testStore = useTestStore();
console.log('testStore',testStore);
console.log('router',router);
console.log('route',route);

const gotoIndex = () => {
  router.navigateTo({
    url: "/pages/test/piniaTest",
    query: {
      name: "uniapp",
    },
  });
};
</script>

<style>
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.logo {
  height: 200rpx;
  width: 200rpx;
  margin-top: 200rpx;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 50rpx;
}

.text-area {
  display: flex;
  justify-content: center;
}

.title {
  font-size: 36rpx;
  color: #8f8f94;
}
</style>
