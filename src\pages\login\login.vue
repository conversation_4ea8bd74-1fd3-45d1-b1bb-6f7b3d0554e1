<template>
  <view class="login-container">
    <view class="login-header">
      <image class="logo" src="@/static/logo.png"></image>
      <text class="app-name">陪护服务</text>
    </view>

    <view class="login-form">
      <view class="form-item">
        <input
          v-model="loginForm.username"
          class="form-input"
          placeholder="请输入用户名/手机号"
          :disabled="loading"
        />
      </view>

      <view class="form-item">
        <input
          v-model="loginForm.password"
          class="form-input"
          type="password"
          placeholder="请输入密码"
          :disabled="loading"
        />
      </view>

      <button
        class="login-btn"
        :class="{ 'login-btn-disabled': loading }"
        :disabled="loading"
        @click="handleLogin"
      >
        {{ loading ? '登录中...' : '登录' }}
      </button>

      <view class="login-tips">
        <text class="tip-text">首次登录请联系管理员获取账号</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { useRouter, useRoute } from "uniapp-router-next";
import { ref, onMounted } from "vue";
import { useUserStore } from "@/store/modules/uesr";

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();

// 登录表单数据
const loginForm = ref({
  username: '',
  password: ''
});

// 加载状态
const loading = ref(false);

// 处理登录
const handleLogin = async () => {
  // 表单验证
  if (!loginForm.value.username.trim()) {
    uni.showToast({
      title: '请输入用户名',
      icon: 'none'
    });
    return;
  }

  if (!loginForm.value.password.trim()) {
    uni.showToast({
      title: '请输入密码',
      icon: 'none'
    });
    return;
  }

  loading.value = true;

  try {
    // 调用登录接口
    await userStore.login({
      username: loginForm.value.username.trim(),
      password: loginForm.value.password.trim()
    });

    uni.showToast({
      title: '登录成功',
      icon: 'success'
    });

    // 登录成功后跳转
    setTimeout(() => {
      const redirectUrl = String(route.query.redirect || '/pages/index/index');
      uni.reLaunch({
        url: redirectUrl
      });
    }, 1500);

  } catch (error) {
    console.error('Login error:', error);
    uni.showToast({
      title: error.message || '登录失败，请重试',
      icon: 'none',
      duration: 2000
    });
  } finally {
    loading.value = false;
  }
};

// 页面加载时检查登录状态
onMounted(() => {
  // 如果已经登录，直接跳转
  if (userStore.hasValidToken) {
    uni.reLaunch({
      url: '/pages/index/index'
    });
  }
});
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 60rpx;
  box-sizing: border-box;
}

.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 120rpx;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  border-radius: 20rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.app-name {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.login-form {
  width: 100%;
  max-width: 600rpx;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-input {
  width: 100%;
  height: 96rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 48rpx;
  padding: 0 40rpx;
  font-size: 32rpx;
  color: #333;
  box-sizing: border-box;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.form-input:focus {
  background: #ffffff;
  border-color: #667eea;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}

.form-input::placeholder {
  color: #999;
}

.login-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 48rpx;
  border: none;
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  margin-top: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.login-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.login-btn-disabled {
  opacity: 0.6;
  transform: none !important;
}

.login-tips {
  margin-top: 60rpx;
  text-align: center;
}

.tip-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}
</style>
