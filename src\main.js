import {
	createSSRApp
} from "vue";
import router from "./router/router";
import { createPinia } from "pinia";
import { createUnistorage } from "pinia-plugin-unistorage";
import uviewPlus from 'uview-plus'
import App from "./App.vue";
import { initAuth } from "./utils/auth";
import { useUserStore } from "./store/modules/uesr";

// 状态管理
const pinia = createPinia();
// 持久化
pinia.use(createUnistorage());

export function createApp() {
	const app = createSSRApp(App);
	app.use(uviewPlus)
	app.use(pinia);
	app.use(router);

	// 初始化认证系统
	const userStore = useUserStore();
	initAuth(userStore);

	return {
		app,
	};
}
