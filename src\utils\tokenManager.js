/**
 * Token管理工具类
 * 负责token的存储、获取、验证和刷新逻辑
 */
class TokenManager {
  constructor() {
    this.ACCESS_TOKEN_KEY = 'access_token'
    this.REFRESH_TOKEN_KEY = 'refresh_token'
    this.TOKEN_EXPIRE_KEY = 'token_expire_time'
    this.userStore = null // 将在初始化时设置
  }

  /**
   * 设置用户store引用
   * @param {Object} store - 用户store实例
   */
  setUserStore(store) {
    this.userStore = store
  }

  /**
   * 获取访问令牌
   * 优先从store获取，如果store中没有则从本地存储获取
   * @returns {string} 访问令牌
   */
  getAccessToken() {
    if (this.userStore && this.userStore.accessToken) {
      return this.userStore.accessToken
    }
    return uni.getStorageSync(this.ACCESS_TOKEN_KEY) || ''
  }

  /**
   * 获取刷新令牌
   * 优先从store获取，如果store中没有则从本地存储获取
   * @returns {string} 刷新令牌
   */
  getRefreshToken() {
    if (this.userStore && this.userStore.refreshToken) {
      return this.userStore.refreshToken
    }
    return uni.getStorageSync(this.REFRESH_TOKEN_KEY) || ''
  }

  /**
   * 获取token过期时间
   * @returns {number} 过期时间戳
   */
  getTokenExpireTime() {
    if (this.userStore && this.userStore.tokenExpireTime) {
      return this.userStore.tokenExpireTime
    }
    return uni.getStorageSync(this.TOKEN_EXPIRE_KEY) || 0
  }

  /**
   * 设置token信息
   * 同时更新store和本地存储
   * @param {string} accessToken - 访问令牌
   * @param {string} refreshToken - 刷新令牌
   * @param {number} expiresIn - 过期时间（秒）
   */
  setTokens(accessToken, refreshToken, expiresIn = 7200) {
    // 计算过期时间（当前时间 + 过期秒数 - 5分钟缓冲时间）
    const expireTime = Date.now() + (expiresIn * 1000) - (5 * 60 * 1000)
    
    // 更新本地存储
    uni.setStorageSync(this.ACCESS_TOKEN_KEY, accessToken)
    uni.setStorageSync(this.REFRESH_TOKEN_KEY, refreshToken)
    uni.setStorageSync(this.TOKEN_EXPIRE_KEY, expireTime)
    
    // 更新store
    if (this.userStore) {
      this.userStore.setTokens(accessToken, refreshToken, expireTime)
    }
  }

  /**
   * 清除所有token
   * 同时清除store和本地存储
   */
  clearTokens() {
    // 清除本地存储
    uni.removeStorageSync(this.ACCESS_TOKEN_KEY)
    uni.removeStorageSync(this.REFRESH_TOKEN_KEY)
    uni.removeStorageSync(this.TOKEN_EXPIRE_KEY)
    
    // 清除store
    if (this.userStore) {
      this.userStore.clearTokens()
    }
  }

  /**
   * 检查token是否过期
   * @returns {boolean} 是否过期
   */
  isTokenExpired() {
    const expireTime = this.getTokenExpireTime()
    return Date.now() >= expireTime
  }

  /**
   * 检查是否有有效的token
   * @returns {boolean} 是否有有效token
   */
  hasValidToken() {
    const accessToken = this.getAccessToken()
    return accessToken && !this.isTokenExpired()
  }

  /**
   * 从本地存储恢复token到store
   */
  restoreTokensFromStorage() {
    const accessToken = uni.getStorageSync(this.ACCESS_TOKEN_KEY)
    const refreshToken = uni.getStorageSync(this.REFRESH_TOKEN_KEY)
    const expireTime = uni.getStorageSync(this.TOKEN_EXPIRE_KEY)
    
    if (accessToken && refreshToken && this.userStore) {
      this.userStore.setTokens(accessToken, refreshToken, expireTime)
    }
  }

  /**
   * 验证token格式是否正确
   * @param {string} token - 要验证的token
   * @returns {boolean} 格式是否正确
   */
  validateTokenFormat(token) {
    if (!token || typeof token !== 'string') {
      return false
    }
    
    // 简单的JWT格式验证（三个部分用.分隔）
    const parts = token.split('.')
    return parts.length === 3
  }

  /**
   * 获取token剩余有效时间（秒）
   * @returns {number} 剩余秒数，如果已过期返回0
   */
  getTokenRemainingTime() {
    const expireTime = this.getTokenExpireTime()
    const remainingMs = expireTime - Date.now()
    return Math.max(0, Math.floor(remainingMs / 1000))
  }

  /**
   * 检查token是否即将过期（5分钟内）
   * @returns {boolean} 是否即将过期
   */
  isTokenExpiringSoon() {
    const remainingTime = this.getTokenRemainingTime()
    return remainingTime > 0 && remainingTime <= 300 // 5分钟
  }
}

// 创建单例实例
const tokenManager = new TokenManager()

export default tokenManager
export { TokenManager }
