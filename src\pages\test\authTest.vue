<template>
  <view class="test-container">
    <view class="test-header">
      <text class="test-title">认证功能测试</text>
    </view>
    
    <view class="test-section">
      <text class="section-title">Token状态</text>
      <view class="info-item">
        <text class="info-label">登录状态:</text>
        <text class="info-value" :class="{ 'success': userStore.isLoggedIn, 'error': !userStore.isLoggedIn }">
          {{ userStore.isLoggedIn ? '已登录' : '未登录' }}
        </text>
      </view>
      <view class="info-item">
        <text class="info-label">Token有效:</text>
        <text class="info-value" :class="{ 'success': userStore.hasValidToken, 'error': !userStore.hasValidToken }">
          {{ userStore.hasValidToken ? '有效' : '无效' }}
        </text>
      </view>
      <view class="info-item">
        <text class="info-label">Access Token:</text>
        <text class="info-value token">{{ userStore.accessToken || '无' }}</text>
      </view>
    </view>
    
    <view class="test-section">
      <text class="section-title">API测试</text>
      <button class="test-btn" @click="testProtectedApi" :disabled="loading">
        {{ loading ? '请求中...' : '测试受保护接口' }}
      </button>
      <button class="test-btn" @click="testTokenRefresh" :disabled="loading">
        {{ loading ? '刷新中...' : '手动刷新Token' }}
      </button>
      <button class="test-btn danger" @click="testLogout" :disabled="loading">
        {{ loading ? '登出中...' : '登出' }}
      </button>
    </view>
    
    <view class="test-section">
      <text class="section-title">测试结果</text>
      <scroll-view class="log-container" scroll-y>
        <view v-for="(log, index) in logs" :key="index" class="log-item">
          <text class="log-time">{{ log.time }}</text>
          <text class="log-content" :class="log.type">{{ log.content }}</text>
        </view>
      </scroll-view>
      <button class="test-btn secondary" @click="clearLogs">清空日志</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/store/modules/uesr'
import request, { tokenManager } from '@/utils/request/uni-request'
import { getTokenRemainingTimeText } from '@/utils/auth'

const userStore = useUserStore()
const loading = ref(false)
const logs = ref([])

// 添加日志
const addLog = (content, type = 'info') => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
  logs.value.unshift({
    time,
    content,
    type
  })
  // 限制日志数量
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

// 测试受保护的API
const testProtectedApi = async () => {
  loading.value = true
  addLog('开始测试受保护接口...', 'info')
  
  try {
    // 这里可以调用任何需要认证的接口
    const response = await request.get('/api/user/profile')
    addLog(`接口调用成功: ${JSON.stringify(response)}`, 'success')
  } catch (error) {
    addLog(`接口调用失败: ${error.message}`, 'error')
  } finally {
    loading.value = false
  }
}

// 测试token刷新
const testTokenRefresh = async () => {
  loading.value = true
  addLog('开始手动刷新Token...', 'info')

  try {
    const oldToken = tokenManager.getAccessToken()
    addLog(`旧Token: ${oldToken.substring(0, 20)}...`, 'info')

    await request.refreshToken()

    const newToken = tokenManager.getAccessToken()
    addLog(`新Token: ${newToken.substring(0, 20)}...`, 'success')
    addLog('Token刷新成功', 'success')
    addLog(`剩余时间: ${getTokenRemainingTimeText()}`, 'info')
  } catch (error) {
    addLog(`Token刷新失败: ${error.message}`, 'error')
  } finally {
    loading.value = false
  }
}

// 测试登出
const testLogout = async () => {
  loading.value = true
  addLog('开始登出...', 'info')
  
  try {
    await userStore.logout()
    addLog('登出成功', 'success')
  } catch (error) {
    addLog(`登出失败: ${error.message}`, 'error')
  } finally {
    loading.value = false
  }
}

// 清空日志
const clearLogs = () => {
  logs.value = []
  addLog('日志已清空', 'info')
}

onMounted(() => {
  addLog('认证测试页面已加载', 'info')
  addLog(`当前登录状态: ${userStore.isLoggedIn ? '已登录' : '未登录'}`, 'info')
})
</script>

<style scoped>
.test-container {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.test-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
}

.test-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  display: block;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 200rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.info-value.success {
  color: #52c41a;
}

.info-value.error {
  color: #ff4d4f;
}

.info-value.token {
  font-family: monospace;
  font-size: 24rpx;
  word-break: break-all;
}

.test-btn {
  width: 100%;
  height: 80rpx;
  background: #1890ff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
  margin-bottom: 20rpx;
}

.test-btn.secondary {
  background: #d9d9d9;
  color: #333;
}

.test-btn.danger {
  background: #ff4d4f;
}

.test-btn:disabled {
  opacity: 0.6;
}

.log-container {
  height: 400rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.log-item {
  margin-bottom: 16rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.log-time {
  font-size: 24rpx;
  color: #999;
  margin-right: 20rpx;
}

.log-content {
  font-size: 28rpx;
  color: #333;
}

.log-content.success {
  color: #52c41a;
}

.log-content.error {
  color: #ff4d4f;
}

.log-content.info {
  color: #1890ff;
}
</style>
