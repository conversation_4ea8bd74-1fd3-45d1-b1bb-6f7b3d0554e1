import axios from 'axios'
import mpAdapter from 'uniapp-axios-adapter'
import tokenManager from '@/utils/tokenManager'

const BASE_API = import.meta.env.VITE_API_ADDRESS

class Request {
  constructor() {
    this.baseUrl = BASE_API
    this.tokenManager = tokenManager
    this.isRefreshing = false
    this.failedQueue = []
    this.setupAxios()
  }

  // 配置axios
  setupAxios() {
    // 配置适配器
    axios.defaults.adapter = mpAdapter

    // 创建axios实例
    this.instance = axios.create({
      baseURL: this.baseUrl,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const accessToken = this.tokenManager.getAccessToken()
        if (accessToken && config.headers) {
          config.headers.Authorization = `Bearer ${accessToken}`
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        return response
      },
      async (error) => {
        const originalRequest = error.config

        // 处理401错误（token过期）
        if (error.response?.status === 401 && !originalRequest._retry) {
          if (this.isRefreshing) {
            // 如果正在刷新token，将请求加入队列
            return new Promise((resolve, reject) => {
              this.failedQueue.push({ resolve, reject })
            }).then(() => {
              return this.instance(originalRequest)
            })
          }

          originalRequest._retry = true
          this.isRefreshing = true

          try {
            await this.refreshToken()
            this.processQueue(null)
            return this.instance(originalRequest)
          } catch (refreshError) {
            this.processQueue(refreshError)
            this.handleAuthError()
            return Promise.reject(refreshError)
          } finally {
            this.isRefreshing = false
          }
        }

        return Promise.reject(error)
      }
    )
  }

  // 处理队列中的请求
  processQueue(error) {
    this.failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error)
      } else {
        resolve()
      }
    })
    this.failedQueue = []
  }

  // 刷新token
  async refreshToken() {
    const refreshToken = this.tokenManager.getRefreshToken()
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    try {
      const response = await axios.post(`${this.baseUrl}/auth/refresh`, {
        refreshToken: refreshToken
      }, {
        adapter: mpAdapter
      })

      // 根据实际API响应结构调整
      const responseData = response.data
      const { accessToken, refreshToken: newRefreshToken, expiresIn = 7200 } = responseData.data || responseData

      this.tokenManager.setTokens(accessToken, newRefreshToken, expiresIn)

      return accessToken
    } catch (error) {
      this.tokenManager.clearTokens()
      throw error
    }
  }

  // 处理认证错误
  handleAuthError() {
    this.tokenManager.clearTokens()
    uni.showToast({
      title: '登录已过期，请重新登录',
      icon: 'none'
    })
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/login/login'
      })
    }, 1500)
  }

  // 处理参数，移除空值
  handleParams(params) {
    if (!params) return {}
    const result = {}
    Object.keys(params).forEach(key => {
      if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
        result[key] = params[key]
      }
    })
    return result
  }

  // 获取token（保持向后兼容）
  getToken() {
    return this.tokenManager.getAccessToken()
  }

  // 处理URL
  handleUrl(path) {
    const Expression = /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\\/?%&=]*)?/;
    const objExp = new RegExp(Expression);
    if (objExp.test(path)) {
      return path
    }
    return path.startsWith('/') ? path : `/${path}`
  }

  // 核心请求方法
  async request(options) {
    const {
      url,
      method = 'GET',
      data,
      params,
      headers = {},
      isNeedToken = true
    } = options

    try {
      // 处理请求参数
      const requestParams = this.handleParams(params)
      const requestData = this.handleParams(data)

      // 构建请求配置
      const config = {
        url: this.handleUrl(url),
        method: method.toLowerCase(),
        headers: { ...headers }
      }

      // 根据请求方法添加数据
      if (['get', 'delete'].includes(config.method)) {
        config.params = requestParams
      } else {
        config.data = requestData
      }

      // 如果不需要token，临时移除Authorization头
      if (!isNeedToken && config.headers) {
        delete config.headers.Authorization
      }

      const response = await this.instance(config)

      // 处理响应数据
      const result = response.data
      if (result.code === 200 || result.success) {
        return result.data || result
      } else {
        throw new Error(result.message || '请求失败')
      }
    } catch (error) {
      // 网络错误处理
      if (!error.response) {
        uni.showToast({
          title: '请检查当前网络环境',
          icon: 'none'
        })
      }
      throw error
    }
  }

  // 登录方法
  async login(loginData) {
    try {
      const response = await this.request({
        url: '/auth/login', // 这里需要根据实际API调整
        method: 'POST',
        data: loginData,
        isNeedToken: false
      })

      // 假设返回的数据结构包含 accessToken, refreshToken, expiresIn
      const { accessToken, refreshToken, expiresIn = 7200 } = response
      this.tokenManager.setTokens(accessToken, refreshToken, expiresIn)

      return response
    } catch (error) {
      throw error
    }
  }

  // 登出方法
  async logout() {
    try {
      await this.request({
        url: '/auth/logout',
        method: 'POST'
      })
    } catch (error) {
      // 即使登出接口失败，也要清除本地token
      console.warn('Logout API failed:', error)
    } finally {
      this.tokenManager.clearTokens()
    }
  }

  // 对外暴露的方法
  get(url, params = {}, options = {}) {
    return this.request({
      url,
      method: 'GET',
      params,
      ...options
    })
  }

  post(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...options
    })
  }

  put(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...options
    })
  }

  delete(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'DELETE',
      data,
      ...options
    })
  }

  // 获取token管理器实例
  getTokenManager() {
    return this.tokenManager
  }
}

const request = new Request()

// 导出token管理器和请求实例
export { tokenManager }
export default request
