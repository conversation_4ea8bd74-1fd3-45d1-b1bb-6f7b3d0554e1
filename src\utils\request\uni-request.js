const BASE_API = import.meta.env.VITE_API_ADDRESS

class Request {
  constructor() {
    this.baseUrl = BASE_API
    this.interceptors = {
      request: [],
      response: []
    }
  }

  // 添加请求拦截器
  addRequestInterceptor(callback) {
    this.interceptors.request.push(callback)
  }

  // 添加响应拦截器
  addResponseInterceptor(callback) {
    this.interceptors.response.push(callback)
  }

  // 处理参数，移除空值
  handleParams(params) {
    if (!params) return {}
    const result = {}
    Object.keys(params).forEach(key => {
      if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
        result[key] = params[key]
      }
    })
    return result
  }

  // 获取token
  getToken() {
    return uni.getStorageSync('token') || ''
  }

  // 处理URL
  handleUrl(path) {
    const Expression = /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\\/?%&=]*)?/;
    const objExp = new RegExp(Expression);
    if (objExp.test(path)) {
      return path
    }
    return `${this.baseUrl}${path}`
  }

  // 核心请求方法
  async request(options) {
    const {
      url,
      method = 'GET',
      data,
      params,
      headers = {},
      isNeedToken = true
    } = options

    // 处理请求参数
    const requestParams = this.handleParams(params)
    const requestData = this.handleParams(data)

    // 处理请求头
    const requestHeaders = { ...headers }
    if (isNeedToken) {
      const token = this.getToken()
      if (token) {
        requestHeaders['Authorization'] = `Bearer ${token}`
      }
    }

    // 执行请求拦截器
    let requestConfig = {
      url: this.handleUrl(url),
      method,
      data: requestData,
      params: requestParams,
      headers: requestHeaders
    }

    for (const interceptor of this.interceptors.request) {
      requestConfig = await interceptor(requestConfig)
    }

    // 发起请求
    return new Promise((resolve, reject) => {
      uni.request({
        ...requestConfig,
        success: async (response) => {
          let result = response.data
          // 执行响应拦截器
          for (const interceptor of this.interceptors.response) {
            result = await interceptor(result)
          }
          // 处理特殊错误码
          if (result.code === 401) {
            uni.removeStorageSync('token')
            uni.reLaunch({
              url: '/pages/login/index'
            })
            reject(new Error('未登录或登录已过期'))
            return
          }
          if (result.code === 200) {
            resolve(result.data)
          } else {
            reject(new Error(result.message || '请求失败'))
          }
        },
        fail: (error) => {
            // 错误处理弹框 当前网络不好
            uni.showToast({
              title: '请检查当前网络环境',
              icon: 'none'
            })
          reject(error)
        }
      })
    })
  }

  // 对外暴露的方法
  get(url, params = {}, options = {}) {
    return this.request({
      url,
      method: 'GET',
      params,
      ...options
    })
  }

  post(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...options
    })
  }

  put(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...options
    })
  }

  delete(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'DELETE',
      data,
      ...options
    })
  }
}

const request = new Request()


export default request
