import request from '@/utils/request/uni-request'

/**
 * 认证相关API
 */
export const authApi = {
  /**
   * 用户登录
   * @param {Object} loginData - 登录数据
   * @param {string} loginData.username - 用户名/手机号
   * @param {string} loginData.password - 密码
   * @returns {Promise} 登录响应
   */
  login(loginData) {
    return request.post('/auth/login', loginData, {
      isNeedToken: false
    })
  },

  /**
   * 刷新token
   * @param {string} refreshToken - 刷新令牌
   * @returns {Promise} 刷新响应
   */
  refreshToken(refreshToken) {
    return request.post('/auth/refresh', {
      refreshToken
    }, {
      isNeedToken: false
    })
  },

  /**
   * 用户登出
   * @returns {Promise} 登出响应
   */
  logout() {
    return request.post('/auth/logout')
  },

  /**
   * 获取用户信息
   * @returns {Promise} 用户信息
   */
  getUserInfo() {
    return request.get('/auth/userinfo')
  },

  /**
   * 验证token有效性
   * @returns {Promise} 验证结果
   */
  validateToken() {
    return request.get('/auth/validate')
  }
}

export default authApi
