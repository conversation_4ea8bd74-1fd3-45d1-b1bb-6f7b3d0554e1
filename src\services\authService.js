/**
 * 认证服务
 * 处理登录、登出、token刷新等认证相关操作
 * 避免循环依赖问题
 */
import { authApi } from '@/apis/auth'

class AuthService {
  constructor() {
    this.userStore = null
  }

  // 设置用户store引用
  setUserStore(store) {
    this.userStore = store
  }

  // 登录
  async login(loginData) {
    try {
      const response = await authApi.login(loginData)
      
      // 设置token信息
      const { accessToken, refreshToken, expiresIn = 7200 } = response
      if (this.userStore) {
        this.userStore.setTokens(accessToken, refreshToken, expiresIn)
        
        // 设置用户信息
        if (response.userInfo) {
          this.userStore.userInfo = response.userInfo
          uni.setStorageSync('userInfo', response.userInfo)
        }
        
        this.userStore.isLoggedIn = true
      }
      
      return response
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    }
  }

  // 登出
  async logout() {
    try {
      await authApi.logout()
    } catch (error) {
      console.warn('Logout API failed:', error)
    } finally {
      if (this.userStore) {
        // 清除状态
        this.userStore.userInfo = null
        this.userStore.isLoggedIn = false
        this.userStore.clearTokens()
        
        // 清除本地存储
        uni.removeStorageSync('userInfo')
      }
      
      // 跳转到登录页
      uni.reLaunch({
        url: '/pages/login/login'
      })
    }
  }

  // 刷新token
  async refreshToken() {
    if (!this.userStore) {
      throw new Error('User store not initialized')
    }

    const refreshToken = this.userStore.refreshToken || ''
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    try {
      const response = await authApi.refreshToken(refreshToken)
      
      // 根据实际API响应结构调整
      const { accessToken, refreshToken: newRefreshToken, expiresIn = 7200 } = response
      
      this.userStore.setTokens(accessToken, newRefreshToken, expiresIn)
      
      return accessToken
    } catch (error) {
      this.userStore.clearTokens()
      throw error
    }
  }

  // 处理认证错误
  handleAuthError() {
    if (this.userStore) {
      this.userStore.clearTokens()
    }
    
    uni.showToast({
      title: '登录已过期，请重新登录',
      icon: 'none'
    })
    
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/login/login'
      })
    }, 1500)
  }

  // 获取访问令牌
  getAccessToken() {
    return this.userStore?.accessToken || ''
  }

  // 获取刷新令牌
  getRefreshToken() {
    return this.userStore?.refreshToken || ''
  }

  // 检查是否有有效token
  hasValidToken() {
    return this.userStore?.hasValidToken || false
  }
}

// 创建单例实例
const authService = new AuthService()

export default authService
export { AuthService }
