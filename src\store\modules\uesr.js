import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import request from '@/utils/request/uni-request'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref(null)
  const isLoggedIn = ref(false)

  // 计算属性
  const hasValidToken = computed(() => {
    const tokenManager = request.getTokenManager()
    return tokenManager.hasValidToken()
  })

  const accessToken = computed(() => {
    const tokenManager = request.getTokenManager()
    return tokenManager.getAccessToken()
  })

  const refreshToken = computed(() => {
    const tokenManager = request.getTokenManager()
    return tokenManager.getRefreshToken()
  })

  // 动作
  const login = async (loginData) => {
    try {
      const response = await request.login(loginData)

      // 设置用户信息
      if (response.userInfo) {
        userInfo.value = response.userInfo
      }

      isLoggedIn.value = true

      // 存储用户信息到本地
      if (userInfo.value) {
        uni.setStorageSync('userInfo', userInfo.value)
      }

      return response
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    }
  }

  const logout = async () => {
    try {
      await request.logout()
    } catch (error) {
      console.warn('Logout API failed:', error)
    } finally {
      // 清除状态
      userInfo.value = null
      isLoggedIn.value = false

      // 清除本地存储
      uni.removeStorageSync('userInfo')

      // 跳转到登录页
      uni.reLaunch({
        url: '/pages/login/login'
      })
    }
  }

  const updateUserInfo = (newUserInfo) => {
    userInfo.value = { ...userInfo.value, ...newUserInfo }
    uni.setStorageSync('userInfo', userInfo.value)
  }

  const initUserState = () => {
    // 从本地存储恢复用户信息
    const storedUserInfo = uni.getStorageSync('userInfo')
    if (storedUserInfo) {
      userInfo.value = storedUserInfo
    }

    // 检查token状态
    const tokenManager = request.getTokenManager()
    isLoggedIn.value = tokenManager.hasValidToken()

    // 如果有用户信息但token无效，清除用户信息
    if (userInfo.value && !isLoggedIn.value) {
      userInfo.value = null
      uni.removeStorageSync('userInfo')
    }
  }

  const checkAuthStatus = () => {
    const tokenManager = request.getTokenManager()
    const hasToken = tokenManager.hasValidToken()

    if (!hasToken && isLoggedIn.value) {
      // token失效，清除登录状态
      logout()
    }

    return hasToken
  }

  return {
    // 状态
    userInfo,
    isLoggedIn,

    // 计算属性
    hasValidToken,
    accessToken,
    refreshToken,

    // 动作
    login,
    logout,
    updateUserInfo,
    initUserState,
    checkAuthStatus
  }
}, {
  // 使用 pinia-plugin-unistorage 持久化部分状态
  unistorage: {
    paths: ['userInfo', 'isLoggedIn']
  }
})