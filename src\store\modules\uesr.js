import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import tokenManager from '@/utils/tokenManager'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref(null)
  const isLoggedIn = ref(false)

  // Token相关状态
  const accessToken = ref('')
  const refreshToken = ref('')
  const tokenExpireTime = ref(0)

  // 计算属性
  const hasValidToken = computed(() => {
    return accessToken.value && tokenExpireTime.value > Date.now()
  })

  const isTokenExpired = computed(() => {
    return tokenExpireTime.value <= Date.now()
  })

  const tokenRemainingTime = computed(() => {
    const remainingMs = tokenExpireTime.value - Date.now()
    return Math.max(0, Math.floor(remainingMs / 1000))
  })

  // Token管理方法
  const setTokens = (newAccessToken, newRefreshToken, expireTime) => {
    accessToken.value = newAccessToken
    refreshToken.value = newRefreshToken
    tokenExpireTime.value = expireTime
  }

  const clearTokens = () => {
    accessToken.value = ''
    refreshToken.value = ''
    tokenExpireTime.value = 0
  }

  // 动作
  const login = async (loginData) => {
    try {
      // 动态导入request以避免循环依赖
      const { default: request } = await import('@/utils/request/uni-request')
      const response = await request.login(loginData)

      // 设置用户信息
      if (response.userInfo) {
        userInfo.value = response.userInfo
      }

      isLoggedIn.value = true

      // 存储用户信息到本地
      if (userInfo.value) {
        uni.setStorageSync('userInfo', userInfo.value)
      }

      return response
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    }
  }

  const logout = async () => {
    try {
      // 动态导入request以避免循环依赖
      const { default: request } = await import('@/utils/request/uni-request')
      await request.logout()
    } catch (error) {
      console.warn('Logout API failed:', error)
    } finally {
      // 清除状态
      userInfo.value = null
      isLoggedIn.value = false
      clearTokens()

      // 清除本地存储
      uni.removeStorageSync('userInfo')

      // 跳转到登录页
      uni.reLaunch({
        url: '/pages/login/login'
      })
    }
  }

  const updateUserInfo = (newUserInfo) => {
    userInfo.value = { ...userInfo.value, ...newUserInfo }
    uni.setStorageSync('userInfo', userInfo.value)
  }

  const initUserState = () => {
    // 设置tokenManager的store引用
    tokenManager.setUserStore({
      accessToken: accessToken.value,
      refreshToken: refreshToken.value,
      tokenExpireTime: tokenExpireTime.value,
      setTokens,
      clearTokens
    })

    // 从本地存储恢复token
    tokenManager.restoreTokensFromStorage()

    // 从本地存储恢复用户信息
    const storedUserInfo = uni.getStorageSync('userInfo')
    if (storedUserInfo) {
      userInfo.value = storedUserInfo
    }

    // 检查token状态
    isLoggedIn.value = hasValidToken.value

    // 如果有用户信息但token无效，清除用户信息
    if (userInfo.value && !hasValidToken.value) {
      userInfo.value = null
      uni.removeStorageSync('userInfo')
    }
  }

  const checkAuthStatus = () => {
    const hasToken = hasValidToken.value

    if (!hasToken && isLoggedIn.value) {
      // token失效，清除登录状态
      logout()
    }

    return hasToken
  }

  return {
    // 状态
    userInfo,
    isLoggedIn,
    accessToken,
    refreshToken,
    tokenExpireTime,

    // 计算属性
    hasValidToken,
    isTokenExpired,
    tokenRemainingTime,

    // 动作
    login,
    logout,
    updateUserInfo,
    initUserState,
    checkAuthStatus,
    setTokens,
    clearTokens
  }
}, {
  // 使用 pinia-plugin-unistorage 持久化部分状态
  unistorage: {
    paths: ['userInfo', 'isLoggedIn', 'accessToken', 'refreshToken', 'tokenExpireTime']
  }
})