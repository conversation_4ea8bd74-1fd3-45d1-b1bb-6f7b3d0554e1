/**
 * 测试导入是否正常工作
 * 用于验证微信小程序编译后的导入问题
 */

// 测试静态导入
import { authApi } from '@/apis/auth'
import request from '@/utils/request/uni-request'
import { useUserStore } from '@/store/modules/uesr'
import { initAuth } from '@/utils/auth'

export function testImports() {
  console.log('Testing imports...')
  
  // 测试authApi
  if (typeof authApi === 'object' && authApi.login) {
    console.log('✅ authApi imported successfully')
  } else {
    console.error('❌ authApi import failed')
  }
  
  // 测试request
  if (typeof request === 'object' && request.get) {
    console.log('✅ request imported successfully')
  } else {
    console.error('❌ request import failed')
  }
  
  // 测试useUserStore
  if (typeof useUserStore === 'function') {
    console.log('✅ useUserStore imported successfully')
  } else {
    console.error('❌ useUserStore import failed')
  }
  
  // 测试initAuth
  if (typeof initAuth === 'function') {
    console.log('✅ initAuth imported successfully')
  } else {
    console.error('❌ initAuth import failed')
  }
  
  console.log('Import test completed')
}

export default {
  testImports
}
